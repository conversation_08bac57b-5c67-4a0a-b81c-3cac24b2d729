* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: white;
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.menu {
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.menu h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #00ff88;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
    letter-spacing: 3px;
}

.menu p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #ccc;
}

.btn {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    border: none;
    padding: 15px 30px;
    font-size: 1.2rem;
    color: white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.5);
    background: linear-gradient(45deg, #00cc6a, #00ff88);
}

.btn:active {
    transform: translateY(0);
}

.game-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.score-board {
    display: flex;
    justify-content: space-between;
    width: 800px;
    max-width: 90vw;
    padding: 0 40px;
}

.score {
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.score span:first-child {
    display: block;
    color: #ccc;
    font-size: 1rem;
    margin-bottom: 5px;
}

.score span:last-child {
    font-size: 2.5rem;
    color: #00ff88;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

#gameCanvas {
    border: 3px solid #00ff88;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
    max-width: 90vw;
    max-height: 60vh;
}

.controls {
    text-align: center;
    color: #ccc;
    font-size: 0.9rem;
}

.final-score {
    font-size: 1.5rem;
    margin: 20px 0;
    color: #00ff88;
    font-weight: bold;
}

.hidden {
    display: none !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .menu h1 {
        font-size: 2rem;
    }
    
    .menu p {
        font-size: 1rem;
    }
    
    .btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
    
    .score-board {
        width: 100%;
        padding: 0 20px;
    }
    
    .score span:last-child {
        font-size: 2rem;
    }
    
    #gameCanvas {
        width: 90vw;
        height: auto;
    }
}

@media (max-width: 480px) {
    .menu {
        padding: 30px 20px;
    }
    
    .menu h1 {
        font-size: 1.8rem;
    }
    
    .score {
        font-size: 1.2rem;
    }
    
    .score span:last-child {
        font-size: 1.8rem;
    }
}
