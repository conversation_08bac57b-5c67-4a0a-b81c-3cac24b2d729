// Game state management
const GameState = {
    MENU: 'menu',
    PLAYING: 'playing',
    GAME_OVER: 'gameOver'
};

class PingPongGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameState = GameState.MENU;
        
        // Game objects
        this.ball = {
            x: 400,
            y: 200,
            radius: 8,
            velocityX: 5,
            velocityY: 3,
            speed: 5
        };
        
        this.playerPaddle = {
            x: 20,
            y: 150,
            width: 15,
            height: 100,
            speed: 8
        };
        
        this.aiPaddle = {
            x: 765,
            y: 150,
            width: 15,
            height: 100,
            speed: 6
        };
        
        this.score = {
            player: 0,
            ai: 0
        };
        
        this.keys = {};
        this.winningScore = 10;
        
        this.initializeEventListeners();
        this.initializeAudio();
    }
    
    initializeEventListeners() {
        // Menu buttons
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('playAgainBtn').addEventListener('click', () => this.resetGame());
        
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.keys[e.key] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key] = false;
        });
    }
    
    initializeAudio() {
        // Create audio context for sound effects
        this.audioContext = null;
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Audio not supported');
        }
    }
    
    playSound(frequency, duration, type = 'sine') {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = type;
        
        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }
    
    startGame() {
        this.gameState = GameState.PLAYING;
        this.showGameArea();
        this.resetBall();
        this.gameLoop();
    }
    
    resetGame() {
        this.score.player = 0;
        this.score.ai = 0;
        this.updateScoreDisplay();
        this.startGame();
    }
    
    showGameArea() {
        document.getElementById('startMenu').classList.add('hidden');
        document.getElementById('gameOverMenu').classList.add('hidden');
        document.getElementById('gameArea').classList.remove('hidden');
    }
    
    showGameOver() {
        document.getElementById('gameArea').classList.add('hidden');
        document.getElementById('gameOverMenu').classList.remove('hidden');
        
        const winner = this.score.player >= this.winningScore ? 'Player' : 'AI';
        document.getElementById('gameOverTitle').textContent = 
            winner === 'Player' ? 'YOU WIN!' : 'AI WINS!';
        document.getElementById('gameOverMessage').textContent = 
            winner === 'Player' ? 'Congratulations!' : 'Better luck next time!';
        document.getElementById('finalPlayerScore').textContent = this.score.player;
        document.getElementById('finalAiScore').textContent = this.score.ai;
    }
    
    resetBall() {
        this.ball.x = this.canvas.width / 2;
        this.ball.y = this.canvas.height / 2;
        this.ball.velocityX = (Math.random() > 0.5 ? 1 : -1) * this.ball.speed;
        this.ball.velocityY = (Math.random() - 0.5) * this.ball.speed;
    }
    
    updateScoreDisplay() {
        document.getElementById('playerScore').textContent = this.score.player;
        document.getElementById('aiScore').textContent = this.score.ai;
    }
    
    update() {
        if (this.gameState !== GameState.PLAYING) return;
        
        // Update player paddle
        this.updatePlayerPaddle();
        
        // Update AI paddle
        this.updateAIPaddle();
        
        // Update ball
        this.updateBall();
        
        // Check for scoring
        this.checkScoring();
        
        // Check for game over
        this.checkGameOver();
    }
    
    updatePlayerPaddle() {
        if (this.keys['ArrowUp'] && this.playerPaddle.y > 0) {
            this.playerPaddle.y -= this.playerPaddle.speed;
        }
        if (this.keys['ArrowDown'] && this.playerPaddle.y < this.canvas.height - this.playerPaddle.height) {
            this.playerPaddle.y += this.playerPaddle.speed;
        }
    }
    
    updateAIPaddle() {
        const paddleCenter = this.aiPaddle.y + this.aiPaddle.height / 2;
        const ballY = this.ball.y;

        if (paddleCenter < ballY - 35) {
            this.aiPaddle.y += this.aiPaddle.speed;
        } else if (paddleCenter > ballY + 35) {
            this.aiPaddle.y -= this.aiPaddle.speed;
        }

        // Keep AI paddle within bounds
        this.aiPaddle.y = Math.max(0, Math.min(this.canvas.height - this.aiPaddle.height, this.aiPaddle.y));
    }

    updateBall() {
        this.ball.x += this.ball.velocityX;
        this.ball.y += this.ball.velocityY;

        // Ball collision with top and bottom walls
        if (this.ball.y - this.ball.radius <= 0 || this.ball.y + this.ball.radius >= this.canvas.height) {
            this.ball.velocityY = -this.ball.velocityY;
            this.playSound(300, 0.1);
        }

        // Ball collision with paddles
        this.checkPaddleCollision();
    }

    checkPaddleCollision() {
        // Player paddle collision
        if (this.ball.x - this.ball.radius <= this.playerPaddle.x + this.playerPaddle.width &&
            this.ball.x + this.ball.radius >= this.playerPaddle.x &&
            this.ball.y >= this.playerPaddle.y &&
            this.ball.y <= this.playerPaddle.y + this.playerPaddle.height) {

            this.ball.velocityX = -this.ball.velocityX;
            this.ball.x = this.playerPaddle.x + this.playerPaddle.width + this.ball.radius;

            // Add some spin based on where the ball hits the paddle
            const hitPos = (this.ball.y - this.playerPaddle.y) / this.playerPaddle.height;
            this.ball.velocityY = (hitPos - 0.5) * 8;

            this.playSound(440, 0.1);
        }

        // AI paddle collision
        if (this.ball.x + this.ball.radius >= this.aiPaddle.x &&
            this.ball.x - this.ball.radius <= this.aiPaddle.x + this.aiPaddle.width &&
            this.ball.y >= this.aiPaddle.y &&
            this.ball.y <= this.aiPaddle.y + this.aiPaddle.height) {

            this.ball.velocityX = -this.ball.velocityX;
            this.ball.x = this.aiPaddle.x - this.ball.radius;

            // Add some spin based on where the ball hits the paddle
            const hitPos = (this.ball.y - this.aiPaddle.y) / this.aiPaddle.height;
            this.ball.velocityY = (hitPos - 0.5) * 8;

            this.playSound(440, 0.1);
        }
    }

    checkScoring() {
        // Player scores (ball goes off right side)
        if (this.ball.x > this.canvas.width) {
            this.score.player++;
            this.updateScoreDisplay();
            this.playSound(600, 0.3);
            this.resetBall();
        }

        // AI scores (ball goes off left side)
        if (this.ball.x < 0) {
            this.score.ai++;
            this.updateScoreDisplay();
            this.playSound(200, 0.3);
            this.resetBall();
        }
    }

    checkGameOver() {
        if (this.score.player >= this.winningScore || this.score.ai >= this.winningScore) {
            this.gameState = GameState.GAME_OVER;
            this.showGameOver();
        }
    }

    draw() {
        // Clear canvas
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw center line
        this.ctx.setLineDash([5, 15]);
        this.ctx.beginPath();
        this.ctx.moveTo(this.canvas.width / 2, 0);
        this.ctx.lineTo(this.canvas.width / 2, this.canvas.height);
        this.ctx.strokeStyle = '#00ff88';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.setLineDash([]);

        // Draw paddles
        this.drawPaddle(this.playerPaddle);
        this.drawPaddle(this.aiPaddle);

        // Draw ball
        this.drawBall();
    }

    drawPaddle(paddle) {
        this.ctx.fillStyle = '#00ff88';
        this.ctx.fillRect(paddle.x, paddle.y, paddle.width, paddle.height);

        // Add glow effect
        this.ctx.shadowColor = '#00ff88';
        this.ctx.shadowBlur = 10;
        this.ctx.fillRect(paddle.x, paddle.y, paddle.width, paddle.height);
        this.ctx.shadowBlur = 0;
    }

    drawBall() {
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fill();

        // Add glow effect
        this.ctx.shadowColor = '#ffffff';
        this.ctx.shadowBlur = 15;
        this.ctx.fill();
        this.ctx.shadowBlur = 0;
    }

    gameLoop() {
        if (this.gameState === GameState.PLAYING) {
            this.update();
            this.draw();
            requestAnimationFrame(() => this.gameLoop());
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new PingPongGame();
});
}
