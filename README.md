# Ping Pong Game

A browser-based Ping Pong game built with HTML5 Canvas, CSS3, and JavaScript.

## Features

- **Responsive Design**: Works on both desktop and mobile browsers
- **Smooth Animations**: Uses `requestAnimationFrame` for 60fps gameplay
- **AI Opponent**: Simple AI that follows the ball with realistic movement
- **Sound Effects**: Audio feedback for paddle hits and scoring
- **Score Tracking**: First to 10 points wins
- **Game States**: Start menu, gameplay, and game over screens
- **Keyboard Controls**: Arrow keys for paddle movement
- **Visual Effects**: Glowing paddles and ball with modern styling

## How to Play

1. Open `index.html` in your web browser
2. Click "Start Game" to begin
3. Use the **Arrow Up** and **Arrow Down** keys to control your paddle
4. Try to hit the ball past the AI paddle to score points
5. First player to reach 10 points wins!
6. Click "Play Again" to start a new match

## Controls

- **↑ Arrow Key**: Move paddle up
- **↓ Arrow Key**: Move paddle down

## Game Mechanics

- Ball bounces off top and bottom walls
- Ball speed and direction change based on where it hits the paddle
- AI paddle follows the ball with slight delay for realistic gameplay
- Score increases when ball passes opponent's paddle
- Game ends when either player reaches 10 points

## Technical Features

- **HTML5 Canvas**: For smooth 2D graphics rendering
- **CSS3**: Modern styling with gradients, shadows, and responsive design
- **Web Audio API**: Procedural sound generation for game effects
- **Object-Oriented JavaScript**: Clean, organized code structure
- **Game State Management**: Proper handling of menu, gameplay, and game over states
- **Collision Detection**: Accurate ball-paddle and ball-wall collision detection

## Browser Compatibility

This game works in all modern browsers that support:
- HTML5 Canvas
- Web Audio API (optional, game works without sound)
- ES6 JavaScript features

## File Structure

```
pinpong/
├── index.html      # Main HTML file
├── styles.css      # CSS styling
├── script.js       # Game logic and JavaScript
└── README.md       # This file
```

## Customization

You can easily customize the game by modifying variables in `script.js`:

- `winningScore`: Change the score needed to win (default: 10)
- `ball.speed`: Adjust ball speed
- `playerPaddle.speed`: Change player paddle speed
- `aiPaddle.speed`: Adjust AI difficulty
- Canvas dimensions in `index.html`

Enjoy playing Ping Pong!
