<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ping Pong Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Start Menu -->
        <div id="startMenu" class="menu">
            <h1>PING PONG</h1>
            <p>Use Arrow Keys to control your paddle</p>
            <button id="startBtn" class="btn">Start Game</button>
        </div>

        <!-- Game Area -->
        <div id="gameArea" class="game-area hidden">
            <div class="score-board">
                <div class="score">
                    <span>Player</span>
                    <span id="playerScore">0</span>
                </div>
                <div class="score">
                    <span>AI</span>
                    <span id="aiScore">0</span>
                </div>
            </div>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <p>Use ↑ ↓ Arrow Keys to move your paddle</p>
            </div>
        </div>

        <!-- Game Over Menu -->
        <div id="gameOverMenu" class="menu hidden">
            <h1 id="gameOverTitle">GAME OVER</h1>
            <p id="gameOverMessage"></p>
            <div class="final-score">
                <span>Final Score: </span>
                <span id="finalPlayerScore">0</span>
                <span> - </span>
                <span id="finalAiScore">0</span>
            </div>
            <button id="playAgainBtn" class="btn">Play Again</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
